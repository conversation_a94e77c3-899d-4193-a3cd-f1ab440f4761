{"name": "homepage", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "npm run lint && npm run type-check && npm run build", "analyze": "ANALYZE=true npm run build", "export": "next build && next export", "preview": "npm run build && npm run start"}, "dependencies": {"@next/font": "^14.2.15", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-next": "^0.6.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}