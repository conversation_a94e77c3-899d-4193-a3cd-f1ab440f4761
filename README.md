# Tianze Wang - Professional Homepage

A modern, sophisticated personal homepage built with Next.js, showcasing AI consulting expertise, photography portfolio, and technology insights.

## 🚀 Features

- **Modern Design**: Clean, professional interface with sophisticated animations
- **Responsive**: Fully responsive design that works on all devices
- **Performance Optimized**: Fast loading with optimized images and code splitting
- **SEO Friendly**: Comprehensive SEO optimization with meta tags and structured data
- **Accessibility**: WCAG compliant with proper semantic HTML and ARIA labels
- **TypeScript**: Fully typed for better development experience
- **Analytics Ready**: Google Analytics and performance monitoring integration

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Fonts**: Inter & JetBrains Mono (Google Fonts)
- **Deployment**: Vercel (recommended)

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── blog/              # Blog section
│   ├── photography/       # Photography portfolio
│   ├── offerings/         # Services page
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── globals.css        # Global styles
│   ├── sitemap.ts         # Sitemap generation
│   ├── robots.ts          # Robots.txt
│   └── manifest.ts        # PWA manifest
├── components/            # Reusable components
│   ├── navigation/        # Header and footer
│   └── ui/               # UI components
├── lib/                  # Utility functions
│   └── utils.ts          # Shared utilities
└── public/               # Static assets
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/personal-homepage.git
cd personal-homepage
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
# Add other variables as needed
```

4. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📝 Content Management

### Updating Personal Information

1. **About Page**: Edit `src/app/about/page.tsx`
2. **Homepage**: Edit `src/app/page.tsx`
3. **Contact Info**: Update footer in `src/components/navigation/Footer.tsx`

### Adding Blog Posts

Currently using static data. To add new posts:

1. Edit the `blogPosts` array in `src/app/blog/page.tsx`
2. For a full CMS integration, consider:
   - Contentful
   - Sanity
   - Strapi
   - Notion API

### Photography Portfolio

1. Replace placeholder images in `src/app/photography/page.tsx`
2. Update the `portfolioImages` array with your photos
3. Consider using a service like Cloudinary for image optimization

## 🎨 Customization

### Design System

The design system is defined in:
- `src/app/globals.css` - CSS variables and utilities
- `src/lib/utils.ts` - Utility functions and variants

### Colors

Primary colors can be customized in `globals.css`:
```css
:root {
  --primary-50: #f0f9ff;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  /* ... */
}
```

### Typography

Font configuration in `src/app/layout.tsx`:
```typescript
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})
```

## 📊 Analytics & Performance

### Google Analytics

1. Get your GA4 tracking ID
2. Add to `.env.local`:
```env
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

### Performance Monitoring

The site includes:
- Web Vitals tracking
- Performance monitoring
- Error tracking (ready for Sentry integration)

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy!

### Other Platforms

The site can be deployed to:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

For static export:
```bash
npm run export
```

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks
- `npm run test` - Run all checks (lint + type-check + build)

## 📱 PWA Support

The site includes PWA capabilities:
- Web app manifest
- Service worker ready
- Offline support (can be enabled)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Animated with [Framer Motion](https://www.framer.com/motion/)
- Icons from [Lucide](https://lucide.dev/)
- Fonts from [Google Fonts](https://fonts.google.com/)

## 📞 Contact

- Email: <EMAIL>
- LinkedIn: [linkedin.com/in/tianzewang-1](https://linkedin.com/in/tianzewang-1)
- Website: [tianzewang.com](https://tianzewang.com)
