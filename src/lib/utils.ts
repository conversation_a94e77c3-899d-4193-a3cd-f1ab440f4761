import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Utility function to merge Tailwind CSS classes with proper precedence
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Typography variants for consistent text styling
 */
export const typography = {
  h1: "text-4xl md:text-5xl lg:text-6xl font-black tracking-tight",
  h2: "text-3xl md:text-4xl lg:text-5xl font-black tracking-tight",
  h3: "text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight",
  h4: "text-xl md:text-2xl lg:text-3xl font-bold tracking-tight",
  h5: "text-lg md:text-xl lg:text-2xl font-bold tracking-tight",
  h6: "text-base md:text-lg lg:text-xl font-bold tracking-tight",
  body: "text-base leading-relaxed",
  bodyLarge: "text-lg leading-relaxed",
  bodySmall: "text-sm leading-relaxed",
  caption: "text-xs text-muted-foreground",
  lead: "text-2xl text-muted-foreground leading-loose px-6 sm:px-8 lg:px-0 py-4 sm:py-6 mb-8 sm:mb-12 max-w-lg sm:max-w-2xl md:max-w-4xl lg:max-w-5xl mx-auto",
  code: "font-mono text-sm bg-muted px-2 py-1 rounded",
}

/**
 * Button variants for consistent button styling
 */
export const buttonVariants = {
  primary: "bg-primary-600 hover:bg-primary-700 text-white shadow-md hover:shadow-lg transition-all duration-200",
  secondary: "bg-secondary-100 hover:bg-secondary-200 text-secondary-900 border border-secondary-200 transition-all duration-200",
  outline: "border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white transition-all duration-200",
  ghost: "text-primary-600 hover:bg-primary-50 transition-all duration-200",
  link: "text-primary-600 hover:text-primary-700 underline-offset-4 hover:underline transition-all duration-200",
}

/**
 * Card variants for consistent card styling
 */
export const cardVariants = {
  default: "bg-card border border-border rounded-lg shadow-sm",
  elevated: "bg-card border border-border rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300",
  glass: "glass rounded-lg",
  gradient: "bg-gradient-to-br from-primary-50 to-secondary-50 border border-border rounded-lg",
}

/**
 * Container variants for consistent layout
 */
export const containerVariants = {
  default: "container mx-auto px-4 sm:px-6 lg:px-8",
  narrow: "container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl",
  wide: "container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl",
  full: "w-full px-4 sm:px-6 lg:px-8",
}

/**
 * Animation variants for consistent motion
 */
export const animationVariants = {
  fadeIn: "animate-fade-in-up",
  slideInLeft: "animate-slide-in-left",
  slideInRight: "animate-slide-in-right",
  pulse: "animate-pulse-slow",
}

/**
 * Spacing utilities
 */
export const spacing = {
  section: "py-16 md:py-24 lg:py-32",
  sectionSmall: "py-8 md:py-12 lg:py-16",
  element: "mb-6 md:mb-8 lg:mb-12",
  elementSmall: "mb-4 md:mb-6",
}

/**
 * Utility to format dates consistently
 */
export function formatDate(date: Date | string): string {
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

/**
 * Utility to truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength).trim() + '...'
}

/**
 * Utility to generate reading time estimate
 */
export function getReadingTime(text: string): string {
  const wordsPerMinute = 200
  const words = text.trim().split(/\s+/).length
  const minutes = Math.ceil(words / wordsPerMinute)
  return `${minutes} min read`
}

/**
 * Utility to validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Utility to generate slug from text
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

/**
 * Utility to get initials from name
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
