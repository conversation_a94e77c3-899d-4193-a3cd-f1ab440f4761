// Content Management System Utilities
// This file provides utilities for accessing and managing centralized content

import { ContentData } from '@/types/content'

// Import all content files
import siteConfigData from '@/content/site-config.json'
import personalData from '@/content/personal-data.json'
import blogPostsData from '@/content/blog-posts.json'
import portfolioData from '@/content/portfolio.json'
import servicesData from '@/content/services.json'

// Combine all content into a single typed object
export const content: ContentData = {
  ...siteConfigData,
  ...personalData,
  ...blogPostsData,
  ...portfolioData,
  ...servicesData
} as ContentData

// Content access utilities
export const getContent = () => content

export const getSiteConfig = () => content.site

export const getPersonalInfo = () => content.personal

export const getNavigation = () => content.navigation

export const getSocialLinks = () => content.socialLinks

export const getSEOMetadata = () => content.seo

export const getManifestData = () => content.manifest

export const getSitemapData = () => content.sitemap

export const getEducation = () => content.education

export const getExperience = () => content.experience

export const getSkills = () => content.skills

export const getHomeFeatures = () => content.homeFeatures

export const getBlogPosts = () => content.blogPosts

export const getFeaturedBlogPosts = () => content.blogPosts.filter(post => post.featured)

export const getRecentBlogPosts = (limit = 4) => 
  content.blogPosts.filter(post => !post.featured).slice(0, limit)

export const getBlogPostById = (id: number) => 
  content.blogPosts.find(post => post.id === id)

export const getBlogPostBySlug = (slug: string) => 
  content.blogPosts.find(post => post.slug === slug)

export const getPortfolioImages = () => content.portfolioImages

export const getPortfolioCategories = () => content.portfolioCategories

export const getPortfolioImagesByCategory = (category: string) => 
  category === 'All' 
    ? content.portfolioImages 
    : content.portfolioImages.filter(img => img.category === category)

export const getServices = () => content.services

export const getAchievements = () => content.achievements

export const getFooterContent = () => content.footer

// Utility functions for dynamic content generation
export const generateSitemapEntries = () => {
  const baseUrl = content.site.url
  const currentDate = new Date().toISOString()

  // Static pages
  const staticPages = content.sitemap.staticPages.map(page => ({
    ...page,
    url: `${baseUrl}${page.url}`,
    lastModified: currentDate
  }))

  // Blog posts
  const blogPosts = content.sitemap.blogPosts.map(post => ({
    ...post,
    url: `${baseUrl}${post.url}`
  }))

  return [...staticPages, ...blogPosts]
}

export const generateManifest = () => ({
  name: content.site.name,
  short_name: content.site.shortName,
  description: content.site.description,
  start_url: '/',
  display: 'standalone' as const,
  background_color: content.site.backgroundColor,
  theme_color: content.site.themeColor,
  orientation: 'portrait-primary' as const,
  scope: '/',
  lang: 'en',
  categories: content.manifest.categories,
  icons: content.manifest.icons,
  screenshots: content.manifest.screenshots
})

// Content validation utilities
export const validateContent = () => {
  const errors: string[] = []

  // Validate required fields
  if (!content.site.name) errors.push('Site name is required')
  if (!content.site.url) errors.push('Site URL is required')
  if (!content.personal.name) errors.push('Personal name is required')
  if (!content.navigation.length) errors.push('Navigation items are required')

  // Validate navigation items
  content.navigation.forEach((item, index) => {
    if (!item.name) errors.push(`Navigation item ${index} is missing name`)
    if (!item.href) errors.push(`Navigation item ${index} is missing href`)
    if (!item.icon) errors.push(`Navigation item ${index} is missing icon`)
  })

  // Validate blog posts
  content.blogPosts.forEach((post, index) => {
    if (!post.title) errors.push(`Blog post ${index} is missing title`)
    if (!post.excerpt) errors.push(`Blog post ${index} is missing excerpt`)
    if (!post.author) errors.push(`Blog post ${index} is missing author`)
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Image management utilities
export const getImageUrl = (src: string) => {
  // Handle external URLs
  if (src.startsWith('http')) {
    return src
  }
  
  // Handle local images
  return src.startsWith('/') ? src : `/${src}`
}

export const getOptimizedImageUrl = (src: string, width?: number, height?: number) => {
  const imageUrl = getImageUrl(src)
  
  // For external images (like Unsplash), we can add optimization parameters
  if (imageUrl.includes('unsplash.com')) {
    const url = new URL(imageUrl)
    if (width) url.searchParams.set('w', width.toString())
    if (height) url.searchParams.set('h', height.toString())
    return url.toString()
  }
  
  return imageUrl
}

// Search and filter utilities
export const searchBlogPosts = (query: string) => {
  const lowercaseQuery = query.toLowerCase()
  return content.blogPosts.filter(post => 
    post.title.toLowerCase().includes(lowercaseQuery) ||
    post.excerpt.toLowerCase().includes(lowercaseQuery) ||
    post.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    post.category.toLowerCase().includes(lowercaseQuery)
  )
}

export const getBlogPostsByCategory = (category: string) => 
  content.blogPosts.filter(post => post.category === category)

export const getBlogPostsByTag = (tag: string) => 
  content.blogPosts.filter(post => post.tags.includes(tag))

export const getAllBlogCategories = () => 
  [...new Set(content.blogPosts.map(post => post.category))]

export const getAllBlogTags = () => 
  [...new Set(content.blogPosts.flatMap(post => post.tags))]
