// Content Management System Types
// This file defines the TypeScript interfaces for the centralized content system

export interface SiteConfig {
  name: string
  shortName: string
  description: string
  url: string
  author: string
  email: string
  keywords: string[]
  themeColor: string
  backgroundColor: string
}

export interface NavigationItem {
  name: string
  href: string
  icon: string // Lucide icon name
}

export interface SocialLink {
  name: string
  href: string
  icon: string // Lucide icon name
  color: string // Tailwind hover color class
}

export interface PersonalInfo {
  name: string
  title: string
  tagline: string
  bio: string
  location: string
  availability: string
  profileImage?: string
}

export interface Education {
  institution: string
  school?: string
  degree: string
  achievement?: string
  period: string
  gpa?: string
  honors?: string
  exchange?: string
  teaching?: string
  location: string
}

export interface WorkExperience {
  company: string
  position: string
  period: string
  previousRole?: string
  location: string
  highlights?: {
    title: string
    achievements: string[]
  }[]
  achievements?: string[]
}

export interface SkillGroup {
  category: string
  items: string[]
  icon: string // Lucide icon name
}

export interface BlogPost {
  id: number
  title: string
  excerpt: string
  content: string
  author: string
  publishedAt: string
  tags: string[]
  category: string
  featured: boolean
  readingTime: string
  slug?: string
}

export interface PortfolioImage {
  id: number
  title: string
  description: string
  src: string
  category: string
  likes: number
  views: number
  alt?: string
}

export interface Service {
  icon: string // Lucide icon name
  title: string
  description: string
  features: string[]
  deliverables: string
  timeline: string
  color: string // Tailwind text color class
  bgColor: string // Tailwind background color class
}

export interface Achievement {
  number: string
  label: string
  description: string
}

export interface HomeFeature {
  icon: string // Lucide icon name
  title: string
  description: string
  href: string
  color: string // Tailwind text color class
}

export interface SEOMetadata {
  title: string
  description: string
  keywords: string[]
  openGraph: {
    title: string
    description: string
    type: string
    locale: string
    siteName: string
  }
  twitter: {
    card: string
    title: string
    description: string
  }
  verification?: {
    google?: string
  }
}

export interface ManifestIcon {
  src: string
  sizes: string
  type: string
  purpose?: string
}

export interface ManifestScreenshot {
  src: string
  sizes: string
  type: string
  form_factor: string
}

export interface SitemapEntry {
  url: string
  lastModified: string
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

// Main content structure
export interface ContentData {
  site: SiteConfig
  personal: PersonalInfo
  navigation: NavigationItem[]
  socialLinks: SocialLink[]
  seo: SEOMetadata
  manifest: {
    icons: ManifestIcon[]
    screenshots: ManifestScreenshot[]
    categories: string[]
  }
  sitemap: {
    staticPages: SitemapEntry[]
    blogPosts: SitemapEntry[]
  }
  education: Education[]
  experience: WorkExperience[]
  skills: SkillGroup[]
  homeFeatures: HomeFeature[]
  blogPosts: BlogPost[]
  portfolioImages: PortfolioImage[]
  portfolioCategories: string[]
  services: Service[]
  achievements: Achievement[]
  footer: {
    description: string
    contactInfo: {
      email: string
      availability: string[]
    }
  }
}
