'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { BookOpen, Calendar, Clock, ArrowRight, User } from 'lucide-react'
import { cn, typography, containerVariants, cardVariants, formatDate } from '@/lib/utils'

// Sample blog posts - replace with actual content management system
const blogPosts = [
  {
    id: 1,
    title: "The Future of GenAI in Pharmaceutical Consulting",
    excerpt: "Exploring how Generative AI is revolutionizing drug discovery, clinical trials, and patient care in the pharmaceutical industry.",
    content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    author: "Tian<PERSON> Wang",
    publishedAt: "2024-01-15",
    tags: ["GenAI", "Pharmaceutical", "Consulting", "Innovation"],
    category: "AI Strategy",
    featured: true,
    readingTime: "8 min read"
  },
  {
    id: 2,
    title: "Building Effective RAG Systems for Enterprise Applications",
    excerpt: "A deep dive into Retrieval-Augmented Generation systems and best practices for implementing them in enterprise environments.",
    content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    author: "Tianze Wang",
    publishedAt: "2024-01-08",
    tags: ["RAG", "LLM", "Enterprise", "Architecture"],
    category: "Technical",
    featured: false,
    readingTime: "12 min read"
  },
  {
    id: 3,
    title: "LLM Deployment Strategies: From Proof of Concept to Production",
    excerpt: "Lessons learned from deploying Large Language Models in production environments, including scalability and performance considerations.",
    content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    author: "Tianze Wang",
    publishedAt: "2024-01-01",
    tags: ["LLM", "Deployment", "Production", "Scalability"],
    category: "Technical",
    featured: false,
    readingTime: "10 min read"
  },
  {
    id: 4,
    title: "AI Ethics in Healthcare: Balancing Innovation and Responsibility",
    excerpt: "Examining the ethical considerations when implementing AI solutions in healthcare and pharmaceutical applications.",
    content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    author: "Tianze Wang",
    publishedAt: "2023-12-20",
    tags: ["AI Ethics", "Healthcare", "Responsibility", "Innovation"],
    category: "Ethics",
    featured: true,
    readingTime: "6 min read"
  },
  {
    id: 5,
    title: "Cross-functional AI Implementation: Lessons from ZS Associates",
    excerpt: "How to successfully implement AI solutions across different departments and stakeholder groups in consulting environments.",
    content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    author: "Tianze Wang",
    publishedAt: "2023-12-15",
    tags: ["Implementation", "Cross-functional", "Consulting", "Leadership"],
    category: "Strategy",
    featured: false,
    readingTime: "7 min read"
  },
  {
    id: 6,
    title: "The Evolution of Prompt Engineering: From Simple Queries to Complex Workflows",
    excerpt: "A comprehensive guide to prompt engineering techniques and how they've evolved to support complex AI workflows.",
    content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    author: "Tianze Wang",
    publishedAt: "2023-12-10",
    tags: ["Prompt Engineering", "AI Workflows", "Best Practices"],
    category: "Technical",
    featured: false,
    readingTime: "9 min read"
  }
]

export default function BlogPage() {
  const featuredPosts = blogPosts.filter(post => post.featured)
  const recentPosts = blogPosts.filter(post => !post.featured).slice(0, 4)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-20 md:py-32">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/20 rounded-full blur-3xl" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-200/20 rounded-full blur-3xl" />
        </div>

        <div className={cn(containerVariants.default, "relative")}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className={cn(typography.h1, "text-gradient mb-6")}>
              <BookOpen className="inline-block w-12 h-12 mr-4 text-primary-600" />
              AI Insights & Blog
            </h1>
            <p className={cn(typography.lead)}>
              Sharing thoughts, discoveries, and insights about artificial intelligence, technology innovation, 
              and the future of AI in business and society.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Featured Posts */}
      {featuredPosts.length > 0 && (
        <section className="py-20 md:py-32 bg-white">
          <div className={containerVariants.default}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className={cn(typography.h2, "mb-4")}>
                Featured Articles
              </h2>
              <p className={cn(typography.lead)}>
                In-depth explorations of AI trends, strategies, and implementations
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredPosts.map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <Link href={`/blog/${post.id}`}>
                    <div className={cn(cardVariants.elevated, "p-8 h-full hover:shadow-xl transition-all duration-300")}>
                      <div className="flex items-center justify-between mb-4">
                        <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                          {post.category}
                        </span>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="w-4 h-4 mr-1" />
                          {post.readingTime}
                        </div>
                      </div>
                      
                      <h3 className="text-2xl font-bold mb-4 group-hover:text-primary-600 transition-colors">
                        {post.title}
                      </h3>
                      
                      <p className="text-muted-foreground leading-relaxed mb-6">
                        {post.excerpt}
                      </p>
                      
                      <div className="flex flex-wrap gap-2 mb-6">
                        {post.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <User className="w-4 h-4 mr-2" />
                          <span>{post.author}</span>
                          <Calendar className="w-4 h-4 ml-4 mr-1" />
                          <span>{formatDate(post.publishedAt)}</span>
                        </div>
                        
                        <div className="flex items-center text-primary-600 group-hover:text-primary-700 transition-colors">
                          <span className="text-sm font-medium mr-2">Read More</span>
                          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Recent Posts */}
      <section className="py-20 md:py-32 bg-secondary-50">
        <div className={containerVariants.default}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              Recent Articles
            </h2>
            <p className={cn(typography.lead)}>
              Latest insights and updates from the world of AI and technology
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {recentPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Link href={`/blog/${post.id}`}>
                  <div className={cn(cardVariants.elevated, "p-6 h-full hover:shadow-lg transition-all duration-300")}>
                    <div className="flex items-center justify-between mb-3">
                      <span className="px-2 py-1 bg-secondary-200 text-secondary-700 rounded text-xs font-medium">
                        {post.category}
                      </span>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="w-3 h-3 mr-1" />
                        {post.readingTime}
                      </div>
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-3 group-hover:text-primary-600 transition-colors line-clamp-2">
                      {post.title}
                    </h3>
                    
                    <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 2).map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-0.5 bg-primary-50 text-primary-600 rounded text-xs"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        <span>{formatDate(post.publishedAt)}</span>
                      </div>
                      
                      <div className="flex items-center text-primary-600 group-hover:text-primary-700 transition-colors">
                        <span className="mr-1">Read</span>
                        <ArrowRight className="w-3 h-3 group-hover:translate-x-0.5 transition-transform" />
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.article>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
