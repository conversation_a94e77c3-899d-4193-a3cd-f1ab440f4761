import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://tianzewang.com'
  const currentDate = new Date().toISOString()

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 1.0,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/photography`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/offerings`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
  ]

  // In a real application, you would fetch dynamic content from your CMS/database
  // For now, we'll add some sample blog posts
  const blogPosts = [
    {
      url: `${baseUrl}/blog/genai-pharmaceutical-consulting`,
      lastModified: '2024-01-15T00:00:00.000Z',
      changeFrequency: 'never' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/blog/rag-systems-enterprise`,
      lastModified: '2024-01-08T00:00:00.000Z',
      changeFrequency: 'never' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/blog/llm-deployment-strategies`,
      lastModified: '2024-01-01T00:00:00.000Z',
      changeFrequency: 'never' as const,
      priority: 0.6,
    },
  ]

  return [...staticPages, ...blogPosts]
}
