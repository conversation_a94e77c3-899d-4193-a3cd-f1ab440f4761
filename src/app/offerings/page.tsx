'use client'

import { motion } from 'framer-motion'
import {
  Briefcase,
  Brain,
  Users,
  TrendingUp,
  Zap,
  CheckCircle,
  Mail
} from 'lucide-react'
import { cn, typography, containerVariants, cardVariants, buttonVariants } from '@/lib/utils'

const services = [
  {
    icon: Brain,
    title: "GenAI Strategy & Implementation",
    description: "End-to-end Generative AI solutions from strategy development to production deployment",
    features: [
      "GenAI use case identification and prioritization",
      "LLM selection and fine-tuning strategies",
      "RAG system architecture and implementation",
      "Prompt engineering and optimization",
      "AI workflow orchestration"
    ],
    deliverables: "Strategic roadmap, MVP development, production deployment",
    timeline: "3-6 months",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    icon: Users,
    title: "Cross-functional AI Training",
    description: "Comprehensive AI education programs for teams and organizations",
    features: [
      "Executive AI literacy workshops",
      "Technical team training on AI tools",
      "Use case development workshops",
      "Change management for AI adoption",
      "Custom curriculum development"
    ],
    deliverables: "Training materials, workshops, certification programs",
    timeline: "1-3 months",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: TrendingUp,
    title: "Pharmaceutical AI Consulting",
    description: "Specialized AI solutions for pharmaceutical and healthcare organizations",
    features: [
      "CRM strategy and AI integration",
      "Sales force effectiveness optimization",
      "Market access and pricing analytics",
      "Clinical trial optimization",
      "Regulatory compliance guidance"
    ],
    deliverables: "Strategic recommendations, implementation roadmap, ROI analysis",
    timeline: "2-4 months",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  },
  {
    icon: Zap,
    title: "AI Tool Development",
    description: "Custom AI tools and applications tailored to specific business needs",
    features: [
      "Custom LLM applications",
      "Data analysis and visualization tools",
      "Automated workflow solutions",
      "API development and integration",
      "Cloud deployment and scaling"
    ],
    deliverables: "Production-ready applications, documentation, support",
    timeline: "2-5 months",
    color: "text-orange-600",
    bgColor: "bg-orange-50"
  }
]

const achievements = [
  {
    number: "5+",
    label: "GenAI Use Cases Developed",
    description: "High-impact MVP products with dedicated prompt design and LLM workflows"
  },
  {
    number: "100+",
    label: "Professionals Trained",
    description: "Comprehensive AI training delivered across pharmaceutical consulting"
  },
  {
    number: "20+",
    label: "Stakeholders Managed",
    description: "Cross-functional alignment across global and local teams"
  },
  {
    number: "90%",
    label: "Efficiency Improvement",
    description: "Time savings achieved through automated data analysis solutions"
  }
]

const testimonials = [
  {
    quote: "Tianze's expertise in GenAI implementation helped us transform our traditional consulting approach and deliver unprecedented value to our pharmaceutical clients.",
    author: "Senior Partner",
    company: "ZS Associates",
    role: "Leadership Team"
  },
  {
    quote: "The AI training program was exceptional - it bridged the gap between technical complexity and practical business application perfectly.",
    author: "Director of Innovation",
    company: "Fortune 500 Pharma",
    role: "Innovation Team"
  }
]

export default function OfferingsPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-20 md:py-32">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/20 rounded-full blur-3xl" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-200/20 rounded-full blur-3xl" />
        </div>

        <div className={cn(containerVariants.default, "relative")}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className={cn(typography.h1, "text-gradient mb-6")}>
              <Briefcase className="inline-block w-12 h-12 mr-4 text-primary-600" />
              My Offerings
            </h1>
            <p className={cn(typography.lead)}>
              Comprehensive AI consulting services combining strategic vision with hands-on implementation expertise. 
              Helping organizations harness the power of artificial intelligence to drive innovation and growth.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <a
                href="mailto:<EMAIL>"
                className={cn(
                  buttonVariants.primary,
                  "px-8 py-4 rounded-lg font-semibold flex items-center space-x-2"
                )}
              >
                <Mail size={20} />
                <span>Get In Touch</span>
              </a>
              
              <a
                href="#services"
                className={cn(
                  buttonVariants.outline,
                  "px-8 py-4 rounded-lg font-semibold"
                )}
              >
                View Services
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Achievements Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className={containerVariants.default}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              Proven Track Record
            </h2>
            <p className={cn(typography.lead)}>
              Delivering measurable results through innovative AI solutions and strategic consulting
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.label}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold text-primary-600 mb-2">
                  {achievement.number}
                </div>
                <div className="text-lg font-semibold text-foreground mb-2">
                  {achievement.label}
                </div>
                <div className="text-muted-foreground text-sm leading-relaxed">
                  {achievement.description}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 md:py-32 bg-secondary-50">
        <div className={containerVariants.default}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              Professional Services
            </h2>
            <p className={cn(typography.lead)}>
              Comprehensive AI consulting services tailored to your organization&apos;s unique needs and challenges
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {services.map((service, index) => {
              const Icon = service.icon
              return (
                <motion.div
                  key={service.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={cn(cardVariants.elevated, "p-8")}
                >
                  <div className="flex items-center mb-6">
                    <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center mr-4", service.bgColor)}>
                      <Icon className={cn("w-6 h-6", service.color)} />
                    </div>
                    <h3 className="text-2xl font-bold text-foreground">{service.title}</h3>
                  </div>
                  
                  <p className="text-muted-foreground leading-relaxed mb-6">
                    {service.description}
                  </p>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-foreground mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, fIndex) => (
                        <li key={fIndex} className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
                          <span className="text-muted-foreground text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-border">
                    <div>
                      <div className="text-sm font-medium text-foreground mb-1">Deliverables</div>
                      <div className="text-sm text-muted-foreground">{service.deliverables}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground mb-1">Timeline</div>
                      <div className="text-sm text-muted-foreground">{service.timeline}</div>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className={cn(containerVariants.wide, "relative")}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              Client Testimonials
            </h2>
            <p className={cn(typography.lead)}>
              What clients say about working with me
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={cn(cardVariants.elevated, "p-8")}
              >
                <blockquote className="text-lg text-muted-foreground leading-relaxed mb-6 italic">
                  {testimonial.quote}
                </blockquote>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-primary-600 font-semibold">
                      {testimonial.author.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.author}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.role}, {testimonial.company}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 md:py-32 bg-gradient-to-br from-primary-600 to-primary-700 text-white">
        <div className={cn(containerVariants.wide, "relative")}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your Organization with AI?
            </h2>
            <p className="text-xl text-primary-100 leading-relaxed mb-8">
              Let&apos;s discuss how we can leverage artificial intelligence to drive innovation and growth in your organization.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <a
                href="mailto:<EMAIL>"
                className="bg-white text-primary-600 hover:bg-primary-50 px-8 py-4 rounded-lg font-semibold flex items-center space-x-2 transition-colors"
              >
                <Mail size={20} />
                <span>Start a Conversation</span>
              </a>
              
              <a
                href="https://linkedin.com/in/tianzewang-1"
                target="_blank"
                rel="noopener noreferrer"
                className="border border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 rounded-lg font-semibold transition-colors"
              >
                Connect on LinkedIn
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
