'use client'

import { motion } from 'framer-motion'
import {
  GraduationCap,
  Briefcase,
  Award,
  MapPin,
  Calendar,
  Users,
  TrendingUp,
  Brain,
  Globe
} from 'lucide-react'
import { cn, typography, containerVariants, cardVariants } from '@/lib/utils'

const education = [
  {
    institution: "University of Michigan, Ann Arbor",
    school: "Stephen <PERSON> School of Business",
    degree: "Master of Management",
    achievement: "Graduation with High Distinction",
    period: "May 2020",
    gpa: "3.85/4.00",
    honors: "Beta Gamma Sigma honor membership",
    location: "Ann Arbor, MI"
  },
  {
    institution: "Shanghai Jiao Tong University",
    school: "University of Michigan – SJTU Joint Institute",
    degree: "Bachelor of Science in Electrical and Computer Engineering",
    achievement: "Outstanding Graduate",
    period: "June 2019",
    exchange: "Exchange Student at ETH Zurich Computer Science Department in 2018 Spring Semester",
    honors: "Merit Student, Academic Excellence and Academic Progress Scholarship, 2018",
    teaching: "Teaching Assistant of 'Intro to Logic Design' and 'Intro to Computer Organization'",
    location: "Shanghai, China"
  }
]

const experience = [
  {
    company: "ZS Associates",
    position: "Associate Consultant",
    period: "2022.7 - Present",
    previousRole: "Business Analyst (2020.5 - 2022.6)",
    location: "Shanghai, China",
    highlights: [
      {
        title: "Generative AI Initiative",
        achievements: [
          "Spearheaded the identification and proposal of Generative AI (GenAI) offerings, developing approximately 5 high-impact use cases with MVP products with dedicated prompt design, LLM Agent workflow, RAG, etc.",
          "Facilitated the GenAI partnership discussion with ZhipuAI and Ali Qwen to jointly promote innovative pharmaceutical GenAI solutions and initiated the pharma GenAI white paper",
          "Innovated the integration of GenAI into traditional consulting methodologies, orchestrated the end-to-end deployment to AWS CN server to serve as internal proof-of-concept",
          "Drove internal GenAI adoption via comprehensive training for 100+ employees, raising firm-wide awareness and expertise in pharma consulting through expertly designed use case scenarios"
        ]
      },
      {
        title: "Key Client Projects",
        achievements: [
          "Led CRM Strategy and Implementation for a Top MNC Pharma, devising use cases for cross-functional alignment and negotiating with 20+ global and local stakeholders",
          "Managed New Product Go-to-Market Action Planning Workshop, facilitating cross-functional alignment across CE, SFE, Marketing, Medical, GAMA, CSO departments",
          "Designed VBP Policy impact-analysis models to help clients finalize Volume Based Procurement decisions and post-VBP pricing strategies",
          "Optimized Sales Force Effectiveness through hospital-level potential assessment and field refinement using Excel and Python models"
        ]
      }
    ]
  },
  {
    company: "GE Aviation Digital",
    position: "Digital Technology Intern",
    period: "2019.1 - 2019.6",
    location: "Shanghai, China",
    achievements: [
      "Initiated a project to automate data aggregation and visualized analysis, saved engineers 90% time compared to traditional Excel method"
    ]
  },
  {
    company: "Intel APAC R&D Center",
    position: "Hardware Test Intern",
    period: "2017.1 - 2017.6",
    location: "Shanghai, China"
  }
]

const skills = [
  {
    category: "AI & Technology",
    items: ["GenAI", "LLM deployment", "RAG", "Python", "R", "C++", "C", "Git"],
    icon: Brain
  },
  {
    category: "Business & Consulting",
    items: ["Strategic Consulting", "Pharmaceutical Industry", "CRM Strategy", "Sales Force Effectiveness"],
    icon: TrendingUp
  },
  {
    category: "Leadership & Collaboration",
    items: ["Cross-functional Leadership", "Stakeholder Management", "Training & Development"],
    icon: Users
  },
  {
    category: "Languages & Interests",
    items: ["Mandarin (Native)", "English (Fluent)", "Calligraphy", "Piano", "Photography"],
    icon: Globe
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-20 md:py-32">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/20 rounded-full blur-3xl" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-200/20 rounded-full blur-3xl" />
        </div>

        <div className={cn(containerVariants.default, "relative")}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className={cn(typography.h1, "text-gradient mb-6")}>
              About Me
            </h1>
            <p className={cn(typography.lead)}>
              A passionate AI consultant with a unique blend of technical expertise and strategic business acumen, 
              dedicated to driving innovation in the pharmaceutical industry through cutting-edge artificial intelligence solutions.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Education Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className={containerVariants.default}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              <GraduationCap className="inline-block w-8 h-8 mr-3 text-primary-600" />
              Education
            </h2>
            <p className={cn(typography.lead)}>
              A strong foundation in both technical engineering and business management from world-class institutions
            </p>
          </motion.div>

          <div className="space-y-8">
            {education.map((edu, index) => (
              <motion.div
                key={edu.institution}
                initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={cn(cardVariants.elevated, "p-8")}
              >
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <h3 className="text-2xl font-bold text-foreground mb-2">{edu.institution}</h3>
                    <p className="text-lg text-primary-600 font-semibold mb-2">{edu.school}</p>
                    <p className="text-lg font-medium mb-2">
                      {edu.degree} {edu.achievement && <span className="text-primary-600">({edu.achievement})</span>}
                    </p>
                    
                    {edu.gpa && (
                      <p className="text-muted-foreground mb-2">GPA: {edu.gpa}</p>
                    )}
                    
                    {edu.honors && (
                      <p className="text-muted-foreground mb-2">{edu.honors}</p>
                    )}
                    
                    {edu.exchange && (
                      <p className="text-muted-foreground mb-2">{edu.exchange}</p>
                    )}
                    
                    {edu.teaching && (
                      <p className="text-muted-foreground">{edu.teaching}</p>
                    )}
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center text-muted-foreground">
                      <Calendar className="w-4 h-4 mr-2" />
                      <span>{edu.period}</span>
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <MapPin className="w-4 h-4 mr-2" />
                      <span>{edu.location}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="py-20 md:py-32 bg-secondary-50">
        <div className={containerVariants.default}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              <Briefcase className="inline-block w-8 h-8 mr-3 text-primary-600" />
              Professional Experience
            </h2>
            <p className={cn(typography.lead)}>
              Leading AI innovation and strategic consulting in the pharmaceutical industry
            </p>
          </motion.div>

          <div className="space-y-12">
            {experience.map((exp, index) => (
              <motion.div
                key={exp.company}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={cn(cardVariants.elevated, "p-8")}
              >
                <div className="mb-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
                    <div>
                      <h3 className="text-2xl font-bold text-foreground mb-1">{exp.company}</h3>
                      <p className="text-xl text-primary-600 font-semibold">{exp.position}</p>
                      {exp.previousRole && (
                        <p className="text-muted-foreground">{exp.previousRole}</p>
                      )}
                    </div>
                    <div className="mt-4 lg:mt-0 space-y-2">
                      <div className="flex items-center text-muted-foreground">
                        <Calendar className="w-4 h-4 mr-2" />
                        <span>{exp.period}</span>
                      </div>
                      <div className="flex items-center text-muted-foreground">
                        <MapPin className="w-4 h-4 mr-2" />
                        <span>{exp.location}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {exp.highlights ? (
                  <div className="space-y-8">
                    {exp.highlights.map((highlight) => (
                      <div key={highlight.title}>
                        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                          <Award className="w-5 h-5 mr-2 text-primary-600" />
                          {highlight.title}
                        </h4>
                        <ul className="space-y-3">
                          {highlight.achievements.map((achievement, aIndex) => (
                            <li key={aIndex} className="flex items-start">
                              <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                              <p className="text-muted-foreground leading-relaxed">{achievement}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                ) : (
                  <ul className="space-y-3">
                    {exp.achievements?.map((achievement, aIndex) => (
                      <li key={aIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                        <p className="text-muted-foreground leading-relaxed">{achievement}</p>
                      </li>
                    ))}
                  </ul>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className={containerVariants.default}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              Skills & Expertise
            </h2>
            <p className={cn(typography.lead)}>
              A comprehensive skill set spanning technology, business strategy, and leadership
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {skills.map((skillGroup, index) => {
              const Icon = skillGroup.icon
              return (
                <motion.div
                  key={skillGroup.category}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={cn(cardVariants.elevated, "p-6")}
                >
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                      <Icon className="w-5 h-5 text-primary-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-foreground">{skillGroup.category}</h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {skillGroup.items.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-3 py-1 bg-secondary-100 text-secondary-700 rounded-full text-sm font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
    </div>
  )
}
