'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { ArrowRight, Sparkles, Code, Camera, BookOpen, Briefcase } from 'lucide-react'
import { cn, typography, buttonVariants, containerVariants } from '@/lib/utils'
import { getPersonalInfo, getHomeFeatures } from '@/lib/content'

// Icon mapping for dynamic icon rendering
const iconMap = {
  Code,
  Camera,
  BookOpen,
  Briefcase,
}

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/30 rounded-full blur-3xl" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-200/30 rounded-full blur-3xl" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-primary-100/20 to-secondary-100/20 rounded-full blur-3xl" />
        </div>

        <div className={cn(containerVariants.default, "relative py-20 md:py-32 lg:py-40")}>
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-6"
            >
              <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-8">
                <Sparkles size={16} />
                <span>AI Consultant & Technology Enthusiast</span>
              </div>

              <h1 className={cn(typography.h1, "text-gradient mb-6")}>
                Tianze Wang
              </h1>

              <p className={cn(typography.lead)}>
                Passionate about leveraging artificial intelligence to solve complex business challenges.
                Combining technical expertise with strategic consulting to drive innovation in the pharmaceutical industry.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6"
            >
              <Link
                href="/about"
                className={cn(
                  buttonVariants.primary,
                  "px-8 py-4 rounded-lg font-semibold flex items-center space-x-2"
                )}
              >
                <span>Learn About Me</span>
                <ArrowRight size={20} />
              </Link>

              <Link
                href="/offerings"
                className={cn(
                  buttonVariants.outline,
                  "px-8 py-4 rounded-lg font-semibold"
                )}
              >
                View My Work
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className={containerVariants.default}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className={cn(typography.h2, "mb-4")}>
              Explore My <span className="text-gradient">Expertise</span>
            </h2>
            <p className={cn(typography.lead)}>
              Discover my professional journey, creative pursuits, and insights across technology and innovation
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                  className="group"
                >
                  <Link href={feature.href}>
                    <div className="bg-white border border-border rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 h-full">
                      <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center mb-4", feature.color, "bg-current/10")}>
                        <Icon className={cn("w-6 h-6", feature.color)} />
                      </div>
                      <h3 className="text-xl font-semibold mb-3 group-hover:text-primary-600 transition-colors">
                        {feature.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </Link>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
    </div>
  )
}
