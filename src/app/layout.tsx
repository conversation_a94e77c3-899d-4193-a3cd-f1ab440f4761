import type { Metadata } from "next";
import { Inter, JetBrains_Mono } from 'next/font/google'
import "./globals.css";
import Header from '@/components/navigation/Header'
import Footer from '@/components/navigation/Footer'
import Analytics, { initializePerformanceMonitoring } from '@/components/ui/Analytics'
import { cn } from '@/lib/utils'
import StagewiseToolbarClient from "@/components/StagewiseToolbarClient";

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
})

import { getSEOMetadata, getSiteConfig } from '@/lib/content'

const seoData = getSEOMetadata()
const siteConfig = getSiteConfig()

export const metadata: Metadata = {
  title: seoData.title,
  description: seoData.description,
  keywords: seoData.keywords,
  authors: [{ name: siteConfig.author }],
  creator: siteConfig.author,
  openGraph: {
    type: seoData.openGraph.type as "website",
    locale: seoData.openGraph.locale,
    url: siteConfig.url,
    title: seoData.openGraph.title,
    description: seoData.openGraph.description,
    siteName: seoData.openGraph.siteName,
  },
  twitter: {
    card: seoData.twitter.card as "summary_large_image",
    title: seoData.twitter.title,
    description: seoData.twitter.description,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: seoData.verification,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Initialize performance monitoring on client side
  if (typeof window !== 'undefined') {
    initializePerformanceMonitoring()
  }



  return (
    <html lang="en" className={cn(inter.variable, jetbrainsMono.variable)}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://www.google-analytics.com" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div className="relative flex min-h-screen flex-col">
          <Header />
                      <main className="flex-1 pt-16 md:pt-20">
              {children}
              <StagewiseToolbarClient />
            </main>
          <Footer />
        </div>

        {/* Analytics and Performance Monitoring */}
        <Analytics
          googleAnalyticsId={process.env.NEXT_PUBLIC_GA_ID}
          enableWebVitals={true}
        />
      </body>
    </html>
  );
}
