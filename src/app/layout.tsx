import type { Metada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from 'next/font/google'
import "./globals.css";
import Header from '@/components/navigation/Header'
import Footer from '@/components/navigation/Footer'
import Analytics, { initializePerformanceMonitoring } from '@/components/ui/Analytics'
import { cn } from '@/lib/utils'
import StagewiseToolbarClient from "@/components/StagewiseToolbarClient";

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
})

export const metadata: Metadata = {
  title: "Tian<PERSON> - AI Consultant & Technology Enthusiast",
  description: "Professional homepage of <PERSON><PERSON><PERSON>, AI consultant specializing in GenAI solutions, pharmaceutical consulting, and technology innovation. Explore my work, photography, and insights on artificial intelligence.",
  keywords: ["<PERSON><PERSON><PERSON>", "AI Consultant", "GenAI", "Artificial Intelligence", "Technology", "Photography", "Blog"],
  authors: [{ name: "<PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://tianzewang.com",
    title: "Tianze Wang - AI Consultant & Technology Enthusiast",
    description: "Professional homepage showcasing AI consulting expertise, photography portfolio, and technology insights.",
    siteName: "Tianze Wang",
  },
  twitter: {
    card: "summary_large_image",
    title: "Tianze Wang - AI Consultant & Technology Enthusiast",
    description: "Professional homepage showcasing AI consulting expertise, photography portfolio, and technology insights.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Initialize performance monitoring on client side
  if (typeof window !== 'undefined') {
    initializePerformanceMonitoring()
  }



  return (
    <html lang="en" className={cn(inter.variable, jetbrainsMono.variable)}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://www.google-analytics.com" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div className="relative flex min-h-screen flex-col">
          <Header />
                      <main className="flex-1 pt-16 md:pt-20">
              {children}
              <StagewiseToolbarClient />
            </main>
          <Footer />
        </div>

        {/* Analytics and Performance Monitoring */}
        <Analytics
          googleAnalyticsId={process.env.NEXT_PUBLIC_GA_ID}
          enableWebVitals={true}
        />
      </body>
    </html>
  );
}
