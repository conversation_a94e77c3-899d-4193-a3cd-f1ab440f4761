{"blogPosts": [{"id": 1, "title": "The Future of GenAI in Pharmaceutical Consulting", "excerpt": "Exploring how Generative AI is revolutionizing drug discovery, clinical trials, and patient care in the pharmaceutical industry.", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.", "author": "<PERSON><PERSON><PERSON>", "publishedAt": "2024-01-15", "tags": ["GenAI", "Pharmaceutical", "Consulting", "Innovation"], "category": "AI Strategy", "featured": true, "readingTime": "8 min read", "slug": "genai-pharmaceutical-consulting"}, {"id": 2, "title": "Building Effective RAG Systems for Enterprise Applications", "excerpt": "A deep dive into Retrieval-Augmented Generation systems and best practices for implementing them in enterprise environments.", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "author": "<PERSON><PERSON><PERSON>", "publishedAt": "2024-01-08", "tags": ["RAG", "LLM", "Enterprise", "Architecture"], "category": "Technical", "featured": false, "readingTime": "12 min read", "slug": "rag-systems-enterprise"}, {"id": 3, "title": "LLM Deployment Strategies: From Proof of Concept to Production", "excerpt": "Lessons learned from deploying Large Language Models in production environments, including scalability and performance considerations.", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "author": "<PERSON><PERSON><PERSON>", "publishedAt": "2024-01-01", "tags": ["LLM", "Deployment", "Production", "Scalability"], "category": "Technical", "featured": false, "readingTime": "10 min read", "slug": "llm-deployment-strategies"}, {"id": 4, "title": "AI Ethics in Healthcare: Balancing Innovation and Responsibility", "excerpt": "Examining the ethical considerations when implementing AI solutions in healthcare and pharmaceutical applications.", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "author": "<PERSON><PERSON><PERSON>", "publishedAt": "2023-12-20", "tags": ["AI Ethics", "Healthcare", "Responsibility", "Innovation"], "category": "Ethics", "featured": true, "readingTime": "6 min read", "slug": "ai-ethics-healthcare"}, {"id": 5, "title": "Cross-functional AI Implementation: Lessons from ZS Associates", "excerpt": "How to successfully implement AI solutions across different departments and stakeholder groups in consulting environments.", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "author": "<PERSON><PERSON><PERSON>", "publishedAt": "2023-12-15", "tags": ["Implementation", "Cross-functional", "Consulting", "Leadership"], "category": "Strategy", "featured": false, "readingTime": "7 min read", "slug": "cross-functional-ai-implementation"}, {"id": 6, "title": "The Evolution of Prompt Engineering: From Simple Queries to Complex Workflows", "excerpt": "A comprehensive guide to prompt engineering techniques and how they've evolved to support complex AI workflows.", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "author": "<PERSON><PERSON><PERSON>", "publishedAt": "2023-12-10", "tags": ["Prompt Engineering", "AI Workflows", "Best Practices"], "category": "Technical", "featured": false, "readingTime": "9 min read", "slug": "prompt-engineering-evolution"}]}