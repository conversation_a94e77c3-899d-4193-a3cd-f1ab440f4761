'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Mail,
  Linkedin,
  Github,
  Twitter,
  ArrowUp,
  Heart,
  Code,
  Camera,
  BookOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { getSocialLinks, getPersonalInfo, getFooterContent, getNavigation } from '@/lib/content'

// Icon mapping for dynamic icon rendering
const iconMap = {
  Mail,
  Linkedin,
  Github,
  Twitter,
  Heart,
  Code,
  Camera,
  BookOpen,
}

export default function Footer() {
  // Get content from centralized system
  const socialLinks = getSocialLinks()
  const personalInfo = getPersonalInfo()
  const footerContent = getFooterContent()
  const navigation = getNavigation()

  // Create quick links from navigation (excluding Home)
  const quickLinks = navigation.filter(item => item.href !== '/').map(item => ({
    name: item.name,
    href: item.href,
    icon: item.icon
  }))

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <footer className="bg-secondary-900 text-secondary-100 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-600/20 to-transparent" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative">
        {/* Main Footer Content */}
        <div className="w-full px-4 sm:px-6 lg:px-8 py-12 md:py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-400 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">
                    {personalInfo.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">{personalInfo.name}</h3>
                  <p className="text-secondary-400 text-sm">{personalInfo.title}</p>
                </div>
              </div>
              <p className="text-secondary-300 leading-relaxed mb-6">
                {footerContent.description}
              </p>
              <div className="flex space-x-4">
                {socialLinks.map((link) => {
                  const Icon = iconMap[link.icon as keyof typeof iconMap]
                  return (
                    <motion.a
                      key={link.name}
                      href={link.href}
                      target={link.href.startsWith('http') ? '_blank' : undefined}
                      rel={link.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                      className={cn(
                        'p-2 rounded-lg bg-secondary-800 text-secondary-400 transition-all duration-200',
                        link.color,
                        'hover:bg-secondary-700 hover:scale-110'
                      )}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {Icon && <Icon size={20} />}
                      <span className="sr-only">{link.name}</span>
                    </motion.a>
                  )
                })}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-6">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => {
                  const Icon = iconMap[link.icon as keyof typeof iconMap]
                  return (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="flex items-center space-x-2 text-secondary-300 hover:text-primary-400 transition-colors duration-200"
                      >
                        {Icon && <Icon size={16} />}
                        <span>{link.name}</span>
                      </Link>
                    </li>
                  )
                })}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-6">Get In Touch</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-secondary-300">
                  <Mail size={16} />
                  <a
                    href={`mailto:${footerContent.contactInfo.email}`}
                    className="hover:text-primary-400 transition-colors duration-200"
                  >
                    {footerContent.contactInfo.email}
                  </a>
                </div>
                <div className="text-secondary-300 text-sm">
                  {footerContent.contactInfo.availability.map((line, index) => (
                    <p key={index}>{line}</p>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-secondary-800">
          <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
              <div className="text-secondary-400 text-sm text-center sm:text-left">
                <p>&copy; {new Date().getFullYear()} {personalInfo.name}. All rights reserved.</p>
                <p className="mt-1">Built with Next.js, TypeScript, and Tailwind CSS</p>
              </div>
              
              <motion.button
                onClick={scrollToTop}
                className="p-2 rounded-lg bg-secondary-800 text-secondary-400 hover:text-primary-400 hover:bg-secondary-700 transition-all duration-200"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Scroll to top"
              >
                <ArrowUp size={20} />
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
