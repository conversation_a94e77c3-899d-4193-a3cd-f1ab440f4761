import Head from 'next/head'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string[]
  ogImage?: string
  ogType?: string
  canonicalUrl?: string
  noIndex?: boolean
  structuredData?: object
}

export default function SEO({
  title = "<PERSON><PERSON><PERSON> - AI Consultant & Technology Enthusiast",
  description = "Professional homepage of <PERSON><PERSON><PERSON>, AI consultant specializing in GenAI solutions, pharmaceutical consulting, and technology innovation.",
  keywords = ["Tianze Wang", "AI Consultant", "GenAI", "Artificial Intelligence", "Technology", "Photography", "Blog"],
  ogImage = "/og-image.jpg",
  ogType = "website",
  canonicalUrl,
  noIndex = false,
  structuredData
}: SEOProps) {
  const fullTitle = title.includes("Tian<PERSON> Wang") ? title : `${title} | Tian<PERSON>`
  
  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(", ")} />
      <meta name="author" content="<PERSON><PERSON><PERSON>" />
      <meta name="creator" content="<PERSON><PERSON><PERSON>" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Robots */}
      <meta name="robots" content={noIndex ? "noindex, nofollow" : "index, follow"} />
      <meta name="googlebot" content={noIndex ? "noindex, nofollow" : "index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"} />
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:site_name" content="Tianze Wang" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#0284c7" />
      <meta name="msapplication-TileColor" content="#0284c7" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
    </Head>
  )
}

// Predefined structured data schemas
export const personSchema = {
  "@context": "https://schema.org",
  "@type": "Person",
  "name": "Tianze Wang",
  "jobTitle": "AI Consultant",
  "description": "AI consultant specializing in GenAI solutions, pharmaceutical consulting, and technology innovation",
  "url": "https://tianzewang.com",
  "sameAs": [
    "https://linkedin.com/in/tianzewang-1",
    "mailto:<EMAIL>"
  ],
  "alumniOf": [
    {
      "@type": "EducationalOrganization",
      "name": "University of Michigan",
      "department": "Stephen M. Ross School of Business"
    },
    {
      "@type": "EducationalOrganization", 
      "name": "Shanghai Jiao Tong University",
      "department": "University of Michigan – SJTU Joint Institute"
    }
  ],
  "worksFor": {
    "@type": "Organization",
    "name": "ZS Associates"
  },
  "knowsAbout": [
    "Artificial Intelligence",
    "Generative AI",
    "Machine Learning",
    "Pharmaceutical Consulting",
    "Technology Strategy",
    "Photography"
  ]
}

export const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Tianze Wang - Professional Homepage",
  "url": "https://tianzewang.com",
  "description": "Professional homepage showcasing AI consulting expertise, photography portfolio, and technology insights",
  "author": {
    "@type": "Person",
    "name": "Tianze Wang"
  },
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://tianzewang.com/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
